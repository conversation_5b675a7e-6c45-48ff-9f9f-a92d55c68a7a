import type { PortableTextBlock } from '@portabletext/types'

/**
 * Utility function to select the appropriate field based on current language
 * @param data - The data object containing both original and English fields
 * @param fieldName - The base field name (without language prefix)
 * @param language - Current language ('ru' or 'gb')
 * @returns The appropriate field value based on language
 */
export function getLocalizedField<T>(
  data: any,
  fieldName: string,
  language: 'ru' | 'gb'
): T {
  if (language === 'gb') {
    const englishField = `en_${fieldName}`
    // Return English field if it exists and has content, otherwise fallback to original
    if (data[englishField] !== undefined && data[englishField] !== null) {
      // For rich text fields, check if array has content
      if (Array.isArray(data[englishField])) {
        return data[englishField].length > 0 ? data[englishField] : data[fieldName]
      }
      // For string fields, check if not empty
      if (typeof data[englishField] === 'string') {
        return data[englishField].trim() !== '' ? data[englishField] : data[fieldName]
      }
      return data[englishField]
    }
  }
  
  // Default to original field for Russian or when English field is not available
  return data[fieldName]
}

/**
 * Utility function specifically for string fields
 */
export function getLocalizedString(
  data: any,
  fieldName: string,
  language: 'ru' | 'gb'
): string {
  return getLocalizedField<string>(data, fieldName, language) || ''
}

/**
 * Utility function specifically for rich text fields
 */
export function getLocalizedRichText(
  data: any,
  fieldName: string,
  language: 'ru' | 'gb'
): PortableTextBlock[] {
  return getLocalizedField<PortableTextBlock[]>(data, fieldName, language) || []
}
