/**
 * Player Cleanup Tool for CARX HLTV
 * 
 * Removes players without photos who aren't in the current CSV data,
 * while preserving players with photos for manual review.
 * 
 * Usage: npm run cleanup-players
 */

const { createAndTestClient } = require('../utils/sanity-client')
const { parsePlayerCSV } = require('../utils/csv-parser')
const { logToolStart, logToolEnd, logProgress, logSuccess, logError, logStats, saveReport } = require('../utils/logger')

async function cleanupPlayers() {
  try {
    logToolStart('PLAYER CLEANUP TOOL', 'Remove Obsolete Players Without Photos')
    
    // Initialize Sanity client and test connection
    const client = await createAndTestClient()
    
    // Parse CSV data to get current active players
    const csvPlayers = await parsePlayerCSV()
    const csvNicknames = new Set(csvPlayers.map(p => p.nickname.toLowerCase().trim()))
    
    // Fetch all players from Sanity
    console.log('🔍 Fetching all players from Sanity...')
    const allPlayers = await client.fetch(`
      *[_type == "player" && !(_id in path("drafts.**"))] {
        _id,
        nickname,
        name,
        elo,
        image
      }
    `)
    
    logSuccess(`Found ${allPlayers.length} players in Sanity`)
    
    // Categorize players
    const playersWithPhotos = allPlayers.filter(player => player.image)
    const playersWithoutPhotos = allPlayers.filter(player => !player.image)
    const playersNotInCSV = allPlayers.filter(player => 
      !csvNicknames.has(player.nickname.toLowerCase().trim())
    )
    
    // Players to remove: no photo AND not in current CSV
    const playersToRemove = playersWithoutPhotos.filter(player => 
      !csvNicknames.has(player.nickname.toLowerCase().trim())
    )
    
    // Players with photos not in CSV (for manual review)
    const playersWithPhotosNotInCSV = playersWithPhotos.filter(player => 
      !csvNicknames.has(player.nickname.toLowerCase().trim())
    )
    
    logStats({
      totalPlayers: allPlayers.length,
      playersWithPhotos: playersWithPhotos.length,
      playersWithoutPhotos: playersWithoutPhotos.length,
      playersNotInCSV: playersNotInCSV.length,
      playersToRemove: playersToRemove.length,
      playersWithPhotosNotInCSV: playersWithPhotosNotInCSV.length
    })
    
    if (playersWithPhotosNotInCSV.length > 0) {
      console.log('\n⚠️  PLAYERS WITH PHOTOS NOT IN CSV (preserved for manual review):')
      playersWithPhotosNotInCSV.forEach(player => {
        console.log(`  • ${player.nickname} (ELO: ${player.elo || 'N/A'})`)
      })
    }
    
    if (playersToRemove.length === 0) {
      console.log('\n✅ No players need to be removed!')
      logToolEnd('PLAYER CLEANUP TOOL', true)
      return
    }
    
    console.log('\n🗑️  PLAYERS TO BE REMOVED (no photo + not in CSV):')
    playersToRemove.forEach(player => {
      console.log(`  • ${player.nickname} (ELO: ${player.elo || 'N/A'})`)
    })
    
    // Confirm deletion
    console.log(`\n⚠️  About to delete ${playersToRemove.length} players. Continue? (y/N)`)
    
    // For automated runs, you might want to add a --force flag
    const readline = require('readline')
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    })
    
    const answer = await new Promise(resolve => {
      rl.question('', resolve)
    })
    rl.close()
    
    if (answer.toLowerCase() !== 'y' && answer.toLowerCase() !== 'yes') {
      console.log('❌ Cleanup cancelled by user')
      logToolEnd('PLAYER CLEANUP TOOL', false)
      return
    }
    
    // Delete players
    let deleteCount = 0
    console.log('\n🗑️  Deleting players...')
    
    for (const player of playersToRemove) {
      try {
        await client.delete(player._id)
        deleteCount++
        logProgress(deleteCount, playersToRemove.length, 'Deleting players')
      } catch (error) {
        logError(`Failed to delete ${player.nickname}: ${error.message}`)
      }
    }
    
    // Generate report
    const report = {
      summary: {
        totalPlayersBeforeCleanup: allPlayers.length,
        playersDeleted: deleteCount,
        playersWithPhotosPreserved: playersWithPhotosNotInCSV.length,
        totalPlayersAfterCleanup: allPlayers.length - deleteCount
      },
      deletedPlayers: playersToRemove.slice(0, deleteCount),
      preservedPlayersWithPhotos: playersWithPhotosNotInCSV
    }
    
    saveReport(report, 'player-cleanup-report.json')
    
    logStats({
      playersDeleted: deleteCount,
      playersWithPhotosPreserved: playersWithPhotosNotInCSV.length,
      totalPlayersRemaining: allPlayers.length - deleteCount
    }, 'CLEANUP RESULTS')
    
    logToolEnd('PLAYER CLEANUP TOOL', true)
    
  } catch (error) {
    logError(`Cleanup failed: ${error.message}`)
    logToolEnd('PLAYER CLEANUP TOOL', false)
    process.exit(1)
  }
}

// Run the cleanup
cleanupPlayers()
